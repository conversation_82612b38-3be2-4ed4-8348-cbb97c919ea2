# LavinMQ Configuration File
# This file configures LavinMQ settings for Docker deployment

[main]
# Data directory (matches Docker volume mount)
data_dir = /var/lib/lavinmq
# Log level: trace, debug, info, warn, error, fatal
log_level = info

[amqp]
# Maximum number of channels per connection
channel_max = 2047
# Maximum frame size in bytes
frame_max = 131072
# Connection heartbeat interval in seconds
heartbeat = 60
# Bind address for AMQP protocol
bind = 0.0.0.0
# AMQP port (matches Docker expose)
port = 5672

[http]
# Bind address for HTTP management interface
bind = 0.0.0.0
# HTTP management port (matches Docker expose)
port = 15672
# Enable HTTP management interface
enabled = true

[mgmt]
# Enable management plugin
enabled = true

[clustering]
# Node name for clustering (if needed)
# node_name = lavinmq@localhost

[limits]
# Maximum number of connections
max_connections = 1000
# Maximum number of queues
max_queues = 1000
# Maximum number of exchanges
max_exchanges = 1000