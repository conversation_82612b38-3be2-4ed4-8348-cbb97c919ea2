package channel_pool

import (
	"log/slog"
	"time"

	"queue-manager/common/pool" // Import the generic pool package
	"queue-manager/common/rabbitmq"

	"github.com/cockroachdb/errors"
)

// ChannelPool defines the interface for a channel pool
type ChannelPool interface {
	// GetChannel gets an available Channel from the pool
	GetChannel() (Channel, error)

	Release(Channel)

	// GetChannelWithTimeout gets a channel with a specific timeout
	GetChannelWithTimeout(timeout time.Duration) (Channel, error)

	// HasConfirms returns whether channels in this pool have confirms enabled
	HasConfirms() bool

	// GetStats returns current pool statistics
	GetStats() (current, inUse, waiters int)

	Close()
}

// ChannelPoolOptions configures the channel pool
type ChannelPoolOptions struct {
	// MaxSize is the maximum number of channels to allow in the pool
	MaxSize int
	// CleanupInterval is how often to check channel health and idle timeout
	CleanupInterval time.Duration
	// EnableConfirms determines if channels should have confirms enabled
	EnableConfirms bool
	// IdleTimeout is how long a channel can remain unused before being closed
	IdleTimeout time.Duration
	// InitialSize is the number of channels to create at startup (optional)
	InitialSize int
	// WaitTimeout is the maximum time to wait for an available channel
	// A zero value means wait indefinitely
	WaitTimeout time.Duration
}

// DefaultChannelPoolOptions provides sensible defaults
func DefaultChannelPoolOptions() ChannelPoolOptions {
	return ChannelPoolOptions{
		MaxSize:         50, // Increased from 10 to handle more concurrent operations
		CleanupInterval: 1 * time.Minute,
		EnableConfirms:  true,
		IdleTimeout:     5 * time.Minute,
		InitialSize:     5, // Increased from 2 to reduce initial channel creation overhead
		WaitTimeout:     30 * time.Second,
	}
}

// channelPool manages a dynamic pool of Channel implementations
type channelPool struct {
	pool            pool.ResourcePool[Channel]
	connection      *rabbitmq.Connection
	confirmsEnabled bool
	logger          *slog.Logger
}

// NewChannelPool creates a new dynamic channel pool
func NewChannelPool(conn *rabbitmq.Connection, options ChannelPoolOptions) (ChannelPool, error) {
	logger := slog.With("component", "channel-pool")

	// Factory function to create new channels
	factory := func() (Channel, error) {
		amqpChannel, err := conn.Channel()
		if err != nil {
			return nil, errors.Wrap(err, "failed to create channel")
		}

		// Create a Channel from the amqp.Channel
		pubChannel, err := NewPublishingChannel(amqpChannel, options.EnableConfirms)
		if err != nil {
			amqpChannel.Close()
			return nil, errors.Wrap(err, "failed to create publishing channel")
		}

		return pubChannel, nil
	}

	// Cleanup function to close channels
	cleanup := func(ch Channel) error {
		if ch != nil && !ch.IsClosed() {
			// The channel has its own reference to the underlying amqp.Channel
			return ch.Close()
		}
		return nil
	}

	// Health check function for channels
	healthCheck := func(ch Channel) bool {
		return ch != nil && !ch.IsClosed()
	}

	// Create generic pool options
	poolOptions := pool.PoolOptions{
		MaxSize:         options.MaxSize,
		CleanupInterval: options.CleanupInterval,
		IdleTimeout:     options.IdleTimeout,
		InitialSize:     options.InitialSize,
		WaitTimeout:     options.WaitTimeout,
	}

	// Create the generic pool
	genericPool, err := pool.New(factory, cleanup, healthCheck, poolOptions)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create channel pool")
	}

	cp := &channelPool{
		pool:            genericPool,
		connection:      conn,
		confirmsEnabled: options.EnableConfirms,
		logger:          logger,
	}

	logger.Info("Channel pool initialized",
		"maxSize", options.MaxSize,
		"initialSize", options.InitialSize,
		"idleTimeout", options.IdleTimeout,
		"confirms", options.EnableConfirms)

	return cp, nil
}

// GetChannel gets an available Channel from the pool
func (p *channelPool) GetChannel() (Channel, error) {
	return p.pool.Get()
}

// GetChannelWithTimeout gets a channel with a specific timeout
func (p *channelPool) GetChannelWithTimeout(timeout time.Duration) (Channel, error) {
	return p.pool.GetWithTimeout(timeout)
}

// HasConfirms returns whether channels in this pool have confirms enabled
func (p *channelPool) HasConfirms() bool {
	return p.confirmsEnabled
}

// GetStats returns current pool statistics
func (p *channelPool) GetStats() (current, inUse, waiters int) {
	return p.pool.GetStats()
}

func (p *channelPool) Release(ch Channel) {
	p.pool.Release(ch)
}

func (p *channelPool) Close() {
	p.pool.Close()
}
