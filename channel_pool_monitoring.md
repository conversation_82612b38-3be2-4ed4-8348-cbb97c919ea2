# Monitoramento de Channel Pools

## Código para adicionar logs de monitoramento

```go
// Adicionar em intervalos regulares para monitorar pools
func (p *Producer) LogPoolStats() {
    current, inUse, waiters := p.channelPool.GetStats()
    p.logger.Info("Channel pool stats",
        "current", current,
        "inUse", inUse, 
        "waiters", waiters)
}

// Adicionar em BaseQueueConsumerGroup
func (c *BaseQueueConsumerGroup) LogPoolStats() {
    if c.pubChannelPool != nil {
        current, inUse, waiters := c.pubChannelPool.GetStats()
        c.logger.Info("Pub channel pool stats",
            "current", current,
            "inUse", inUse,
            "waiters", waiters)
    }
}
```

## Configurações Recomendadas para Produção

### LavinMQ (lavinmq.ini)
```ini
[amqp]
channel_max = 65535
frame_max = 131072
heartbeat = 60
```

### Channel Pool Sizes por Componente
- **Producer pools**: 50-100 canais
- **Consumer pub pools**: 200+ canais (para retry/DLQ)
- **Shared queue consumers**: 1 canal por worker (não pooled)

## Sinais de Problemas

### Logs para Observar:
1. "timed out waiting for available resource" - Pool saturado
2. "Channel pool stats" com waiters > 0 - Contenção
3. "failed to create channel" - Limite LavinMQ atingido

### Métricas Importantes:
- Número total de canais ativos
- Waiters em pools
- Taxa de criação/destruição de canais
- Tempo de espera por canais

## Otimizações Futuras

1. **Connection Pooling**: Considerar múltiplas conexões para distribuir canais
2. **Pool Sharing**: Compartilhar pools entre componentes similares
3. **Dynamic Sizing**: Ajustar tamanhos de pool baseado na carga
